/**
 * Gemini AI服务模块
 * 负责与Google Gemini API的交互，提供订单内容智能解析功能
 * 支持实时分析和批量处理
 * 重构为传统script标签加载方式
 *
 * 配置信息：
 * - 模型版本：Gemini 2.0 Flash (gemini-2.5-flash-lite-preview-06-17)
 * - API密钥：内嵌配置（个人自用项目）
 * - 智能ID填充：基于api return id list.md数据映射
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块（延迟获取以确保加载顺序）
    function getAppState() {
        return window.OTA.appState || window.appState;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

class GeminiService {
    constructor() {
        // 内嵌API密钥配置（个人自用项目，忽略安全警告）
        this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';

        // 更新为Gemini 2.0 Flash模型
        this.modelVersion = 'gemini-2.5-flash-lite-preview-06-17';
        this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.modelVersion}:generateContent`;
        this.timeout = 30000;
        
        // 实时分析配置
        this.realtimeConfig = {
            enabled: true,
            debounceDelay: 1500, // 1.5秒防抖延迟
            minInputLength: 20, // 最小输入长度才触发分析
            maxRetries: 2, // 最大重试次数
            confidenceThreshold: 0.3 // 最低置信度阈值
        };
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };

        // 智能ID填充映射表（基于api return id list.md）
        this.idMappings = {
            // 后台用户映射：邮箱 -> ID
            backendUsers: {
                '<EMAIL>': 37,
                '<EMAIL>': 89,
                '<EMAIL>': 310,
                '<EMAIL>': 311,
                '<EMAIL>': 312,
                'SMW <EMAIL>': 342,
                'SMW <EMAIL>': 343,
                '<EMAIL>': 420,
                '<EMAIL>': 421,
                '空空@gomyhire.com': 777,
                '<EMAIL>': 1047,
                '<EMAIL>': 1181,
                '<EMAIL>': 1201,
                'Swee <EMAIL>': 1652,
                'Skymirror <EMAIL>': 2249,
                '<EMAIL>': 2446,
                '<EMAIL>': 2666
            },
            // 子分类映射
            subCategories: [
                { id: 2, name: 'Pickup' },
                { id: 3, name: 'Dropoff' },
                { id: 4, name: 'Charter' }
            ],
            // 车型映射（基于乘客人数，优先使用5 Seater）
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
            ],
            // 行驶区域映射
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' },
                { id: 5, name: 'Singapore (SG)' },
                { id: 6, name: '携程专车 (CTRIP)' },
                { id: 8, name: 'Complete (COMPLETE)' },
                { id: 9, name: 'Paging (PG)' },
                { id: 10, name: 'Charter (CHRT)' },
                { id: 12, name: 'Malacca (MLK)' },
                { id: 13, name: 'SMW (SMW)' }
            ],
            // 语言映射
            languages: [
                { id: 2, name: 'English (EN)' },
                { id: 3, name: 'Malay (MY)' },
                { id: 4, name: 'Chinese (CN)' },
                { id: 5, name: 'Paging (PG)' },
                { id: 6, name: 'Charter (CHARTER)' },
                { id: 8, name: '携程司导 (IM)' },
                { id: 9, name: 'PSV (PSV)' },
                { id: 10, name: 'EVP (EVP)' },
                { id: 11, name: 'Car Type Reverify (CAR)' },
                { id: 12, name: 'Jetty (JETTY)' },
                { id: 13, name: 'PhotoSkill Proof (PHOTO)' }
            ]
        };

        this.orderParsingPrompt = `
你是一个专业的OTA订单处理助手。请解析以下订单描述，提取关键信息并以JSON格式返回。

请从订单描述中提取以下信息（如果存在）：
1. 客户姓名 (customer_name)
2. 客户联系电话 (customer_contact) - 包括国际区号
3. 客户邮箱 (customer_email)
4. 航班信息 (flight_info) - 航班号
5. 上车地点 (pickup) - 尽可能详细，包括具体地址、酒店名称、航站楼、闸口等。如果涉及机场，请明确注明是哪个机场和航站楼。
6. 下车地点 (dropoff) - 尽可能详细，包括具体地址、酒店名称、航站楼等。如果涉及机场，请明确注明是哪个机场和航站楼。
7. 上车日期 (pickup_date) - 格式：YYYY-MM-DD
8. 上车时间 (pickup_time) - 格式：HH:MM (24小时制)
9. 乘客人数 (passenger_count) - 纯数字
10. 行李数量 (luggage_count) - 纯数字
11. 服务类型 (sub_category_id) - 根据ID参考表返回对应ID。
12. 车型 (car_type_id) - 根据ID参考表返回对应ID。
13. 行驶区域 (driving_region_id) - 根据ID参考表返回对应ID。
14. 语言要求 (languages_id_array) - 根据ID参考表返回对应的ID数组。
15. 备注 (remark) - 任何其他重要信息，如“婴儿座椅”、“轮椅无障碍”、“特殊要求”等。
16. 飞行类型 (flight_type) - 仅限 'Arrival' 或 'Departure'。
17. 机场 (airport) - 明确机场名称。
18. 酒店名称 (hotel_name) - 如果上车/下车地点是酒店。
19. OTA参考号 (ota_reference_number)
20. 价格 (price) - 纯数字

ID参考表：

子分类 (sub_category_id):
2: 接机 (Pickup)
3: 送机 (Dropoff)
4: 包车 (Charter)

车型 (car_type_id):
38: 4 Seater Hatchback (3 passenger, 2 x L size luggage)
5: 5 Seater (3 passenger, 3 x L size luggage)
33: Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)
37: Extended 5 (4 passenger, 4 x L size luggage)
35: 7 Seater SUV (4 passenger, 4 x L size luggage)
15: 7 Seater MPV (5 passenger, 4 x L size luggage)
16: Standard Size MPV (5 passenger, 4 x L size luggage)
31: Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)
32: Velfire/ Alphard (6 passenger, 4 x L size luggage)
36: Alphard (6 passenger, 4 x L size luggage)
20: 10 Seater MPV / Van (7 passenger, 7 x L size luggage)
30: 12 seat Starex (7 passenger, 7 x L size luggage)
23: 14 Seater Van (10 passenger, 10 x L size luggage)
24: 18 Seater Van (12 passenger, 12 x L size luggage)
25: 30 Seat Mni Bus (29 passenger, 29 x L size luggage)
26: 44 Seater Bus (43 passenger, 43 x L size luggage)
34: Ticket (N/A passenger, N/A luggage)
39: Ticket (Non-Malaysian) (N/A passenger, N/A luggage)

行驶区域 (driving_region_id):
1: Kl/selangor (KL)
2: Penang (PNG)
3: Johor (JB)
4: Sabah (SBH)
5: Singapore (SG)
6: 携程专车 (CTRIP)
8: Complete (COMPLETE)
9: Paging (PG)
10: Charter (CHRT)
12: Malacca (MLK)
13: SMW (SMW)

语言要求 (languages_id_array):
2: English (EN)
3: Malay (MY)
4: Chinese (CN)
5: Paging (PG)
6: Charter (CHARTER)
8: 携程司导 (IM)
9: PSV (PSV)
10: EVP (EVP)
11: Car Type Reverify (CAR)
12: Jetty (JETTY)
13: PhotoSkill Proof (PHOTO)

负责人 (incharge_by_backend_user_id): 由前端根据登录账号邮箱自动插入，Gemini 无需处理此字段。

**重要规则：**
- 始终返回JSON格式。
- 严格按照ID参考表提供对应的ID，不要返回名称或其他文本。
- 如果订单描述中没有明确信息，对应的JSON字段可以留空或null。
- 特别注意服务类型 (sub_category_id)，仅在订单描述明确为 '接机', '送机', '包车' 时返回对应的ID，否则留空或null。

示例订单描述:
"客户姓名: 张三, 电话: +8613800138000, 航班: CA123, 2024-07-20 14:30, 从吉隆坡国际机场KLIA1到双威酒店, 2人, 2行李, 需要中文司机"

示例JSON输出:
{"customer_name": "张三", "customer_contact": "+8613800138000", "flight_info": "CA123", "pickup_date": "2024-07-20", "pickup_time": "14:30", "pickup": "吉隆坡国际机场KLIA1", "dropoff": "双威酒店", "passenger_count": 2, "luggage_count": 2, "sub_category_id": 2, "car_type_id": 5, "driving_region_id": 1, "languages_id_array": [4], "remark": "需要中文司机", "flight_type": "Arrival", "airport": "吉隆坡国际机场KLIA1", "hotel_name": "双威酒店", "ota_reference_number": null, "price": null}

尽可能准确地提取信息，并保持字段格式一致性。
        `;
    }
    
    /**
     * 设置Gemini API密钥（已内嵌配置）
     * @param {string} apiKey - API密钥
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        getAppState().set('config.geminiApiKey', apiKey);
        getLogger().log('Gemini API密钥已更新', 'info', {
            model: this.modelVersion,
            keyLength: apiKey ? apiKey.length : 0
        });
    }

    /**
     * 获取API密钥（使用内嵌配置）
     * @returns {string} API密钥
     */
    getApiKey() {
        // 优先使用内嵌的API密钥
        if (this.apiKey) {
            return this.apiKey;
        }

        // 备用：从状态管理获取
        const saved = getAppState().get('config.geminiApiKey');
        if (saved) {
            this.apiKey = saved;
            return saved;
        }

        // 如果都没有，返回内嵌密钥
        this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        return this.apiKey;
    }
    
    /**
     * 检查API是否可用
     * @returns {boolean} 是否可用
     */
    isAvailable() {
        return this.getApiKey() !== null;
    }

    /**
     * 智能ID填充功能
     * 基于解析结果和映射表自动填充系统ID
     * @param {object} parsedData - 解析后的订单数据
     * @returns {object} 填充ID后的数据
     */
    performSmartIdFilling(parsedData) {
        const result = { ...parsedData };
        const fillResults = {
            subCategory: { success: false, value: null, error: null },
            carType: { success: false, value: null, error: null },
            drivingRegion: { success: false, value: null, error: null },
            languages: { success: false, value: null, error: null },
            backendUser: { success: false, value: null, error: null }
        };

        // 1. 智能填充子分类ID（限制范围）
        try {
            if (result.sub_category_id) {
                result.sub_category_id = this.getSubCategoryId(result.sub_category_id);
                fillResults.subCategory = { success: true, value: result.sub_category_id, error: null };
            }
        } catch (error) {
            fillResults.subCategory = { success: false, value: null, error: error.message };
            getLogger().log('子分类ID填充失败', 'warning', { error: error.message, input: result.sub_category_id });
        }

        // 2. 智能填充车型ID（基于乘客人数，默认使用Comfort 5 Seater）
        try {
            if (!result.car_type_id) {
                result.car_type_id = this.getCarTypeId(result.passenger_count);
                fillResults.carType = { success: true, value: result.car_type_id, error: null };
            }
        } catch (error) {
            fillResults.carType = { success: false, value: null, error: error.message };
            getLogger().log('车型ID填充失败', 'warning', { error: error.message, passengerCount: result.passenger_count });
            // 提供默认车型作为降级方案
            result.car_type_id = 5; // 默认5座车型
        }

        // 3. 智能填充行驶区域ID（基于地点信息）
        try {
            if ((result.pickup || result.dropoff) && !result.driving_region_id) {
                const locationText = `${result.pickup || ''} ${result.dropoff || ''}`;
                result.driving_region_id = this.getDrivingRegionId(locationText);
                fillResults.drivingRegion = { success: true, value: result.driving_region_id, error: null };
            }
        } catch (error) {
            fillResults.drivingRegion = { success: false, value: null, error: error.message };
            getLogger().log('行驶区域ID填充失败', 'warning', { error: error.message, locations: { pickup: result.pickup, dropoff: result.dropoff } });
            // 提供默认区域作为降级方案
            result.driving_region_id = 1; // 默认吉隆坡区域
        }

        // 4. 智能填充语言ID（基于客户姓名和特殊要求）
        try {
            if (!result.languages_id_array) {
                result.languages_id_array = this.getLanguagesIdArray(result.remark, result.customer_name);
                fillResults.languages = { success: true, value: result.languages_id_array, error: null };
            }
        } catch (error) {
            fillResults.languages = { success: false, value: null, error: error.message };
            getLogger().log('语言ID填充失败', 'warning', { error: error.message, customerName: result.customer_name });
            // 提供默认语言作为降级方案
            result.languages_id_array = { "0": "2" }; // 默认英文
        }

        // 5. 智能填充后台用户ID（基于当前登录用户）
        try {
            if (!result.incharge_by_backend_user_id) {
                result.incharge_by_backend_user_id = this.getBackendUserId();
                fillResults.backendUser = { success: true, value: result.incharge_by_backend_user_id, error: null };
            }
        } catch (error) {
            fillResults.backendUser = { success: false, value: null, error: error.message };
            getLogger().log('后台用户ID填充失败', 'warning', { error: error.message });
            // 提供默认用户作为降级方案
            result.incharge_by_backend_user_id = 37; // 默认smw用户
        }

        // 统计填充结果
        const successCount = Object.values(fillResults).filter(r => r.success).length;
        const totalCount = Object.keys(fillResults).length;

        getLogger().log('智能ID填充完成', successCount === totalCount ? 'success' : 'warning', {
            successRate: `${successCount}/${totalCount}`,
            results: {
                subCategoryId: result.sub_category_id,
                carTypeId: result.car_type_id,
                drivingRegionId: result.driving_region_id,
                languagesIdArray: result.languages_id_array,
                backendUserId: result.incharge_by_backend_user_id
            },
            fillResults
        });

        return result;
    }

    /**
     * 获取子分类ID（限制范围）
     * @param {string} serviceType - 服务类型
     * @returns {number|null} 子分类ID
     */
    getSubCategoryId(serviceType) {
        const serviceTypeLower = serviceType.toLowerCase();

        for (const category of this.idMappings.subCategories) {
            if (category.name.toLowerCase().includes(serviceTypeLower)) {
                getLogger().log('匹配到子分类', 'info', {
                    serviceType,
                    categoryId: category.id,
                    categoryName: category.name
                });
                return category.id;
            }
        }

        // 默认返回接机服务
        getLogger().log('使用默认子分类（接机）', 'warning', { serviceType });
        return 2;
    }

    /**
     * 获取车型ID（基于乘客人数，默认使用Comfort 5 Seater）
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 车型ID
     */
    getCarTypeId(passengerCount) {
        // 如果没有乘客人数信息，使用映射表中乘客上限最小的车型作为默认
        if (!passengerCount || isNaN(parseInt(passengerCount))) {
            const defaultCar = this.idMappings.carTypes[0];
            const defaultId = defaultCar ? defaultCar.id : 1;
            getLogger().log('无乘客人数信息，使用默认车型', 'info', {
                defaultCarTypeId: defaultId
            });
            return defaultId;
        }

        const count = parseInt(passengerCount);

        for (const carType of this.idMappings.carTypes) {
            if (count <= carType.passengerLimit) {
                getLogger().log('推荐车型', 'info', {
                    passengerCount: count,
                    carTypeId: carType.id,
                    carTypeName: carType.name
                });
                return carType.id;
            }
        }

        // 默认返回最大车型
        const defaultCarType = this.idMappings.carTypes[this.idMappings.carTypes.length - 1];
        getLogger().log('使用最大车型', 'warning', {
            passengerCount: count,
            carTypeId: defaultCarType.id
        });
        return defaultCarType.id;
    }

    /**
     * 获取行驶区域ID（基于地点信息）
     * @param {string} locationText - 地点文本
     * @returns {number} 行驶区域ID
     */
    getDrivingRegionId(locationText) {
        const textLower = locationText.toLowerCase();

        for (const region of this.idMappings.drivingRegions) {
            if (region.name.toLowerCase().includes(textLower)) {
                getLogger().log('匹配到行驶区域', 'info', {
                    locationText,
                    regionId: region.id,
                    regionName: region.name
                });
                return region.id;
            }
        }

        // 默认返回KL/Selangor区域
        getLogger().log('使用默认行驶区域（KL）', 'warning', { locationText });
        return 1;
    }

    /**
     * 获取语言ID数组（基于客户姓名和特殊要求）
     * @param {string} extraRequirement - 特殊要求文本
     * @param {string} customerName - 客户姓名
     * @returns {object} 语言ID对象格式
     */
    getLanguagesIdArray(extraRequirement, customerName = '') {
        const matchedLanguages = [];

        // 检测中文字符的正则表达式
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;

        // 优先基于客户姓名检测语言
        if (customerName && chineseRegex.test(customerName)) {
            matchedLanguages.push(4); // Chinese (ID: 4)
            getLogger().log('基于客户姓名检测到中文', 'info', {
                customerName,
                selectedLanguage: 'Chinese'
            });
        }

        // 如果特殊要求中有语言关键词，也添加进去
        if (extraRequirement) {
            const textLower = extraRequirement.toLowerCase();

            for (const language of this.idMappings.languages) {
                if (language.name.toLowerCase().includes(textLower)) {
                    if (!matchedLanguages.includes(language.id)) {
                        matchedLanguages.push(language.id);
                    }
                    getLogger().log('匹配到语言要求', 'info', {
                        requirement: extraRequirement,
                        languageId: language.id,
                        languageName: language.name
                    });
                }
            }
        }

        // 如果没有匹配到任何语言，默认使用英文
        if (matchedLanguages.length === 0) {
            matchedLanguages.push(2); // English (ID: 2)
            getLogger().log('使用默认英文语言', 'info', {
                customerName: customerName || '无',
                extraRequirement: extraRequirement || '无'
            });
        }

        // 转换为对象格式（GoMyHire API要求）
        const languagesObject = {};
        matchedLanguages.forEach((id, index) => {
            languagesObject[index.toString()] = id.toString();
        });

        return languagesObject;
    }

    /**
     * 获取后台用户ID（基于当前登录用户）
     * @returns {number} 后台用户ID
     */
    getBackendUserId() {
        const currentUser = getAppState().get('auth.user');

        if (currentUser && currentUser.email) {
            const userId = this.idMappings.backendUsers[currentUser.email];
            if (userId) {
                getLogger().log('匹配到后台用户', 'info', {
                    email: currentUser.email,
                    userId
                });
                return userId;
            }
        }

        // 默认返回general用户
        getLogger().log('使用默认后台用户（general）', 'warning');
        return 1;
    }
    
    /**
     * 启用/禁用实时分析
     * @param {boolean} enabled - 是否启用
     */
    setRealtimeAnalysis(enabled) {
        this.realtimeConfig.enabled = enabled;
        getAppState().set('config.realtimeAnalysis', enabled);
        
        if (!enabled && this.analysisState.currentRequest) {
            this.cancelCurrentAnalysis();
        }
        
        getLogger().log(`实时分析已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 配置实时分析参数
     * @param {object} config - 配置参数
     */
    configureRealtimeAnalysis(config) {
        this.realtimeConfig = { ...this.realtimeConfig, ...config };
        getLogger().log('实时分析配置已更新', 'info', this.realtimeConfig);
    }
    
    /**
     * 实时分析订单内容（带防抖）
     * @param {string} orderText - 订单描述文本
     * @param {function} onProgress - 进度回调
     * @param {function} onResult - 结果回调
     * @param {function} onError - 错误回调
     * @returns {Promise<void>}
     */
    async analyzeRealtime(orderText, onProgress, onResult, onError) {
        // 检查是否启用实时分析
        if (!this.realtimeConfig.enabled) {
            return;
        }
        
        // 检查输入长度
        if (!orderText || orderText.trim().length < this.realtimeConfig.minInputLength) {
            this.clearAnalysisState();
            return;
        }
        
        // 检查是否与上次分析的内容相同
        if (orderText.trim() === this.analysisState.lastAnalyzedText) {
            return;
        }
        
        // 取消当前正在进行的分析
        this.cancelCurrentAnalysis();
        
        // 创建新的分析请求
        const analysisId = Date.now();
        this.analysisState.isAnalyzing = true;
        this.analysisState.currentRequest = analysisId;
        
        try {
            // 通知开始分析
            if (onProgress) {
                onProgress('开始智能分析...', 0);
            }
            
            getLogger().log('开始实时AI分析', 'info', { 
                inputLength: orderText.length,
                analysisId 
            });
            
            // 执行分析
            const result = await this.parseOrderWithRetry(orderText, analysisId, onProgress);
            
            // 检查请求是否已被取消
            if (this.analysisState.currentRequest !== analysisId) {
                getLogger().log('分析请求已被取消', 'info', { analysisId });
                return;
            }
            
            // 更新分析状态
            this.analysisState.lastAnalyzedText = orderText.trim();
            this.analysisState.isAnalyzing = false;
            this.analysisState.currentRequest = null;
            
            // 记录分析历史
            this.recordAnalysisHistory(orderText, result);
            
            // 通知分析完成
            if (onResult) {
                onResult(result);
            }
            
            getLogger().log('实时AI分析完成', 'success', { 
                analysisId,
                confidence: result.confidence,
                fieldsExtracted: Object.keys(result.data || {}).length
            });
            
        } catch (error) {
            // 检查请求是否已被取消
            if (this.analysisState.currentRequest !== analysisId) {
                return;
            }
            
            this.analysisState.isAnalyzing = false;
            this.analysisState.currentRequest = null;
            
            getLogger().log('实时AI分析失败', 'error', { 
                analysisId,
                error: error.message 
            });
            
            // 尝试基本解析作为降级方案
            const fallbackResult = this.basicParse(orderText);
            if (Object.keys(fallbackResult).length > 0) {
                const result = {
                    success: false,
                    data: fallbackResult,
                    confidence: 0.2,
                    error: error.message,
                    fallback: true
                };
                
                if (onResult) {
                    onResult(result);
                }
            } else if (onError) {
                onError(error);
            }
        }
    }
    
    /**
     * 带重试机制的订单解析
     * @param {string} orderText - 订单文本
     * @param {number} analysisId - 分析ID
     * @param {function} onProgress - 进度回调
     * @returns {Promise<object>} 解析结果
     */
    async parseOrderWithRetry(orderText, analysisId, onProgress) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.realtimeConfig.maxRetries + 1; attempt++) {
            try {
                // 检查请求是否已被取消
                if (this.analysisState.currentRequest !== analysisId) {
                    throw new Error('分析请求已被取消');
                }
                
                if (onProgress) {
                    const progress = (attempt - 1) / (this.realtimeConfig.maxRetries + 1) * 80;
                    onProgress(`AI分析中... (尝试 ${attempt})`, progress);
                }
                
                const result = await this.parseOrder(orderText);
                
                // 检查置信度
                if (result.confidence < this.realtimeConfig.confidenceThreshold) {
                    getLogger().log(`分析置信度过低: ${result.confidence}`, 'warning');
                    
                    if (attempt <= this.realtimeConfig.maxRetries) {
                        continue; // 重试
                    }
                }
                
                if (onProgress) {
                    onProgress('分析完成', 100);
                }
                
                return result;
                
            } catch (error) {
                lastError = error;
                
                if (attempt <= this.realtimeConfig.maxRetries) {
                    getLogger().log(`分析尝试 ${attempt} 失败，准备重试`, 'warning', { 
                        error: error.message 
                    });
                    
                    // 等待一段时间后重试
                    await this.sleep(1000 * attempt);
                } else {
                    throw lastError;
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 取消当前分析
     */
    cancelCurrentAnalysis() {
        if (this.analysisState.currentRequest) {
            getLogger().log('取消当前分析请求', 'info', { 
                requestId: this.analysisState.currentRequest 
            });
            
            this.analysisState.currentRequest = null;
            this.analysisState.isAnalyzing = false;
        }
    }
    
    /**
     * 清除分析状态
     */
    clearAnalysisState() {
        this.cancelCurrentAnalysis();
        this.analysisState.lastAnalyzedText = '';
    }
    
    /**
     * 记录分析历史
     * @param {string} input - 输入内容
     * @param {object} result - 分析结果
     */
    recordAnalysisHistory(input, result) {
        const historyItem = {
            timestamp: new Date().toISOString(),
            input: input.substring(0, 100), // 限制长度
            result: {
                success: result.success,
                confidence: result.confidence,
                fieldsCount: Object.keys(result.data || {}).length
            }
        };
        
        this.analysisState.analysisHistory.push(historyItem);
        
        // 保持最近50条记录
        if (this.analysisState.analysisHistory.length > 50) {
            this.analysisState.analysisHistory = this.analysisState.analysisHistory.slice(-50);
        }
    }
    
    /**
     * 获取分析统计信息
     * @returns {object} 统计信息
     */
    getAnalysisStats() {
        const history = this.analysisState.analysisHistory;
        const total = history.length;
        
        if (total === 0) {
            return {
                total: 0,
                successRate: 0,
                averageConfidence: 0,
                averageFields: 0
            };
        }
        
        const successful = history.filter(item => item.result.success).length;
        const totalConfidence = history.reduce((sum, item) => sum + (item.result.confidence || 0), 0);
        const totalFields = history.reduce((sum, item) => sum + (item.result.fieldsCount || 0), 0);
        
        return {
            total,
            successRate: (successful / total * 100).toFixed(1),
            averageConfidence: (totalConfidence / total).toFixed(2),
            averageFields: (totalFields / total).toFixed(1)
        };
    }
    
    /**
     * 发送请求到Gemini API
     * @param {string} prompt - 提示内容
     * @returns {Promise<string>} AI响应
     */
    async request(prompt) {
        const apiKey = this.getApiKey();
        if (!apiKey) {
            throw new Error('Gemini API密钥未设置');
        }
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        
        try {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1, // 降低温度以提高一致性
                    topK: 20,
                    topP: 0.8,
                    maxOutputTokens: 1024,
                }
            };
            
            getLogger().log('Gemini API请求', 'info', { promptLength: prompt.length });
            
            const response = await fetch(`${this.baseURL}?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`API错误 ${response.status}: ${errorData.error?.message || response.statusText}`);
            }
            
            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('AI未返回有效响应');
            }
            
            const content = data.candidates[0].content?.parts?.[0]?.text;
            if (!content) {
                throw new Error('AI响应格式错误');
            }
            
            getLogger().log('Gemini API响应成功', 'success', { responseLength: content.length });
            return content;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('AI请求超时');
            }
            
            getLogger().log('Gemini API错误', 'error', { error: error.message });
            throw error;
        }
    }
    
    /**
     * 解析订单内容
     * @param {string} orderText - 订单描述文本
     * @returns {Promise<object>} 解析结果
     */
    async parseOrder(orderText) {
        if (!orderText || typeof orderText !== 'string') {
            throw new Error('订单内容不能为空');
        }
        
        try {
            getLogger().log('开始AI解析订单', 'info', { inputLength: orderText.length });
            
            const fullPrompt = `${this.orderParsingPrompt}\n\n订单描述：\n${orderText}`;
            const response = await this.request(fullPrompt);
            
            // 提取JSON内容
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('AI响应中未找到有效的JSON格式');
            }
            
            const parsedData = JSON.parse(jsonMatch[0]);
            
            // 后处理解析结果
            const processedData = this.postProcessParsedData(parsedData);

            // 执行智能ID填充
            const dataWithIds = this.performSmartIdFilling(processedData);

            getLogger().log('AI解析成功', 'success', {
                extractedFields: Object.keys(dataWithIds).length,
                data: dataWithIds,
                smartIdFilling: {
                    subCategoryId: dataWithIds.sub_category_id,
                    carTypeId: dataWithIds.car_type_id,
                    backendUserId: dataWithIds.incharge_by_backend_user_id
                }
            });

            return {
                success: true,
                data: dataWithIds,
                confidence: this.calculateConfidence(dataWithIds),
                rawResponse: response
            };
            
        } catch (error) {
            getLogger().log('AI解析失败', 'error', { 
                error: error.message, 
                input: orderText.substring(0, 100) + '...' 
            });
            
            throw error;
        }
    }
    
    /**
     * 后处理解析数据
     * @param {object} data - 原始解析数据
     * @returns {object} 处理后的数据
     */
    postProcessParsedData(data) {
        const processed = { ...data };
        
        // 处理电话号码
        if (processed.customer_contact) {
            processed.customer_contact = this.normalizePhoneNumber(processed.customer_contact);
        }
        
        // 处理日期格式
        if (processed.pickup_date) {
            processed.pickup_date = this.normalizeDate(processed.pickup_date);
        }
        
        // 处理时间格式
        if (processed.pickup_time) {
            processed.pickup_time = this.normalizeTime(processed.pickup_time);
        }
        
        // 处理数字字段
        ['passenger_count', 'luggage_count', 'price'].forEach(field => {
            if (processed[field]) {
                const num = parseFloat(processed[field]);
                if (!isNaN(num)) {
                    processed[field] = num;
                }
            }
        });
        
        // 处理布尔字段
        ['baby_chair', 'tour_guide', 'meet_and_greet'].forEach(field => {
            if (processed[field]) {
                processed[field] = Boolean(processed[field]);
            }
        });
        
        // 地点标准化
        processed.pickup = this.normalizeLocation(processed.pickup);
        processed.dropoff = this.normalizeLocation(processed.dropoff);
        
        return processed;
    }
    
    /**
     * 标准化电话号码（保持原始格式，不自动添加国家代码）
     * @param {string} phone - 原始电话号码
     * @returns {string} 标准化后的电话号码
     */
    normalizePhoneNumber(phone) {
        if (!phone) return phone;

        // 只移除多余的空格和特殊字符，保持原始格式
        let cleaned = phone.trim();

        // 移除多余的空格和连字符，但保持基本格式
        cleaned = cleaned.replace(/\s+/g, ' ').replace(/-+/g, '-');

        getLogger().log('电话号码标准化', 'info', {
            original: phone,
            normalized: cleaned,
            note: '保持原始格式，未自动添加国家代码'
        });

        return cleaned;
    }
    
    /**
     * 标准化日期
     * @param {string} date - 原始日期
     * @returns {string} 标准化后的日期 (YYYY-MM-DD)
     */
    normalizeDate(date) {
        if (!date) return date;
        
        try {
            // 尝试解析各种日期格式
            const parsed = new Date(date);
            if (isNaN(parsed)) {
                // 尝试其他格式
                const formats = [
                    /(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})/,  // DD/MM/YYYY or DD-MM-YYYY
                    /(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/   // YYYY/MM/DD or YYYY-MM-DD
                ];
                
                for (const format of formats) {
                    const match = date.match(format);
                    if (match) {
                        const [, a, b, c] = match;
                        // 假设第一种格式是DD/MM/YYYY
                        if (c.length === 4) {
                            return `${c}-${b.padStart(2, '0')}-${a.padStart(2, '0')}`;
                        } else {
                            return `${a}-${b.padStart(2, '0')}-${c.padStart(2, '0')}`;
                        }
                    }
                }
            }
            
            return parsed.toISOString().split('T')[0];
        } catch (error) {
            return date;
        }
    }
    
    /**
     * 标准化时间
     * @param {string} time - 原始时间
     * @returns {string} 标准化后的时间 (HH:MM)
     */
    normalizeTime(time) {
        if (!time) return time;
        
        // 提取时间格式
        const timeMatch = time.match(/(\d{1,2}):?(\d{2})\s*(AM|PM)?/i);
        if (timeMatch) {
            let [, hours, minutes, period] = timeMatch;
            hours = parseInt(hours);
            
            if (period) {
                const isPM = period.toUpperCase() === 'PM';
                if (isPM && hours !== 12) {
                    hours += 12;
                } else if (!isPM && hours === 12) {
                    hours = 0;
                }
            }
            
            return `${hours.toString().padStart(2, '0')}:${minutes}`;
        }
        
        return time;
    }
    
    /**
     * 标准化地点名称
     * @param {string} location - 原始地点
     * @returns {string} 标准化后的地点
     */
    normalizeLocation(location) {
        if (!location) return location;
        
        // 常见地点映射
        const locationMap = {
            'KLIA': '吉隆坡国际机场 (KLIA)',
            'KLIA2': '吉隆坡第二国际机场 (KLIA2)',
            'KUL': '吉隆坡国际机场 (KLIA)',
            'KLCC': '吉隆坡城中城',
            'Bukit Bintang': '武吉免登',
            'Chinatown': '茨厂街唐人街',
            'KL Sentral': '吉隆坡中央车站'
        };
        
        // 检查是否有匹配的标准名称
        for (const [key, value] of Object.entries(locationMap)) {
            if (location.toUpperCase().includes(key.toUpperCase())) {
                return value;
            }
        }
        
        return location;
    }
    
    /**
     * 计算解析置信度
     * @param {object} data - 解析数据
     * @returns {number} 置信度 (0-1)
     */
    calculateConfidence(data) {
        const fields = Object.keys(data);
        const importantFields = ['customer_name', 'pickup', 'destination', 'date', 'time'];
        
        let score = 0;
        let maxScore = 0;
        
        // 基础字段分数
        fields.forEach(field => {
            maxScore += 1;
            if (data[field] && data[field] !== '') {
                score += 1;
                
                // 重要字段额外分数
                if (importantFields.includes(field)) {
                    score += 1;
                    maxScore += 1;
                }
            }
        });
        
        return Math.min(score / maxScore, 1);
    }
    
    /**
     * 基本解析（降级方案）
     * @param {string} text - 订单文本
     * @returns {object} 基本解析结果
     */
    basicParse(text) {
        const result = {};
        
        // 提取电话号码（保持原始格式）
        const phoneMatch = text.match(/(\+?60)?[\s-]?(\d{2,3})[\s-]?(\d{3,4})[\s-]?(\d{4})/);
        if (phoneMatch) {
            result.customer_contact = phoneMatch[0].trim();
        }
        
        // 提取邮箱
        const emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        if (emailMatch) {
            result.customer_email = emailMatch[0];
        }
        
        // 提取日期
        const dateMatch = text.match(/(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})/);
        if (dateMatch) {
            const [, day, month, year] = dateMatch;
            result.date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
        
        // 提取时间
        const timeMatch = text.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
            result.time = `${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}`;
        }
        
        // 提取人数
        const passengerMatch = text.match(/(\d+)\s*(?:人|passenger|pax)/i);
        if (passengerMatch) {
            result.passenger_number = parseInt(passengerMatch[1]);
        }
        
        return result;
    }
    
    /**
     * 等待指定时间
     * @param {number} ms - 毫秒数
     * @returns {Promise} Promise对象
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 获取服务状态
     * @returns {object} 服务状态
     */
    getStatus() {
        return {
            available: this.isAvailable(),
            hasApiKey: this.getApiKey() !== null,
            realtimeEnabled: this.realtimeConfig.enabled,
            isAnalyzing: this.analysisState.isAnalyzing,
            lastUsed: getAppState().get('system.lastGeminiCall') || null,
            analysisStats: this.getAnalysisStats()
        };
    }
    
    /**
     * 生成示例订单数据
     * @returns {string} 示例订单文本
     */
    generateSampleOrder() {
        const samples = [
            `客户：张三先生 +60123456789
接送：KLIA2机场 到 Pavilion KL购物中心
时间：2024-03-15 14:30
人数：2大1小
要求：需要儿童座椅
价格：RM 120`,
            
            `预订人：李小姐 (<EMAIL>)
航班：MH370 15:45抵达
从：吉隆坡国际机场
到：Bukit Bintang武吉免登
日期：明天下午4点
乘客：3人 + 2件大行李
特殊要求：司机能说中文`,
            
            `Customer: John Smith +60987654321
From: KL Sentral Station  
To: Genting Highlands
Date: 2024-03-20 09:00
Passengers: 4 adults
Luggage: 3 large suitcases
Service: Charter tour (8 hours)
Price: RM 350`
        ];
        
        return samples[Math.floor(Math.random() * samples.length)];
    }

    /**
     * 根据系统数据动态更新智能ID映射表
     * @param {object} systemData - 通过 ApiService.getAllSystemData() 获取的最新系统数据
     */
    updateIdMappings(systemData) {
        try {
            if (!systemData) return;

            // 1. 动态更新后台用户映射（email -> id）
            if (Array.isArray(systemData.backendUsers)) {
                const backendMap = {};
                systemData.backendUsers.forEach(user => {
                    if (user.email) backendMap[user.email] = user.id;
                });
                this.idMappings.backendUsers = backendMap;
            }

            // 2. 动态更新车型映射（按乘客上限升序排序，首个元素作为默认车型）
            if (Array.isArray(systemData.carTypes)) {
                this.idMappings.carTypes = systemData.carTypes
                    .map(ct => ({
                        id: ct.id,
                        name: ct.name,
                        passengerLimit: parseInt(ct.passenger_limit || ct.passengerLimit || 3)
                    }))
                    .sort((a, b) => a.passengerLimit - b.passengerLimit);
            }

            // 3. 动态更新行驶区域映射（简单使用名称拆分为关键词）
            if (Array.isArray(systemData.drivingRegions)) {
                this.idMappings.drivingRegions = systemData.drivingRegions.map(r => ({
                    id: r.id,
                    name: r.name,
                    keywords: r.name.toLowerCase().split(/[^a-z]+/).filter(Boolean)
                }));
            }

            // 4. 动态更新语言映射（名称首词作为关键词）
            if (Array.isArray(systemData.languages)) {
                this.idMappings.languages = systemData.languages.map(l => ({
                    id: l.id,
                    name: l.name,
                    keywords: [l.name.split(' ')[0].toLowerCase()]
                }));
            }

            getLogger().log('Gemini ID 映射已同步', 'success', {
                backendUsers: Object.keys(this.idMappings.backendUsers).length,
                carTypes: this.idMappings.carTypes.length,
                drivingRegions: this.idMappings.drivingRegions.length,
                languages: this.idMappings.languages.length
            });
        } catch (err) {
            getLogger().logError('同步 Gemini ID 映射失败', err);
        }
    }
}

    // 创建全局Gemini服务实例
    const geminiService = new GeminiService();

    // 暴露到OTA命名空间
    window.OTA.geminiService = geminiService;

    // 向后兼容：暴露到全局window对象
    window.geminiService = geminiService;

})();