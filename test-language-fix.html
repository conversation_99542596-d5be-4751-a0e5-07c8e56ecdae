<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言ID修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-left: 4px solid #28a745; }
        .warning { border-left: 4px solid #ffc107; }
        .error { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 语言ID修复测试</h1>
        <p>测试修复后的智能ID填充功能，特别是语言ID数组处理</p>
        
        <div class="test-section">
            <h3>测试1：中文客户姓名</h3>
            <button class="btn" onclick="testChineseName()">测试中文姓名（索润德）</button>
            <div id="result1" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试2：英文客户姓名</h3>
            <button class="btn" onclick="testEnglishName()">测试英文姓名（John Smith）</button>
            <div id="result2" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试3：空语言数组修复</h3>
            <button class="btn" onclick="testEmptyArray()">测试空数组修复</button>
            <div id="result3" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试4：完整订单解析</h3>
            <button class="btn" onclick="testFullOrder()">测试完整订单</button>
            <div id="result4" class="result"></div>
        </div>
    </div>

    <!-- 加载OTA系统模块 -->
    <script>
        window.OTA = window.OTA || {};
    </script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>

    <script>
        // 等待模块加载
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化系统数据
            if (window.OTA && window.OTA.apiService) {
                window.OTA.apiService.getAllSystemData();
            }
        });

        function testChineseName() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const geminiService = window.OTA.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                // 模拟AI解析结果（中文姓名）
                const mockData = {
                    customer_name: "索润德",
                    customer_contact: "18616536999",
                    sub_category_id: 3,
                    car_type_id: 5,
                    driving_region_id: 1,
                    languages_id_array: [], // 空数组，需要修复
                    incharge_by_backend_user_id: 37
                };
                
                const result = geminiService.performSmartIdFilling(mockData);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `测试成功！
修复前: languages_id_array = []
修复后: languages_id_array = ${JSON.stringify(result.languages_id_array, null, 2)}

预期结果: 应该包含中文语言ID (4)`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        function testEnglishName() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const geminiService = window.OTA.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                // 模拟AI解析结果（英文姓名）
                const mockData = {
                    customer_name: "John Smith",
                    customer_contact: "+60123456789",
                    sub_category_id: 2,
                    car_type_id: 5,
                    driving_region_id: 1,
                    languages_id_array: [], // 空数组，需要修复
                    incharge_by_backend_user_id: 37
                };
                
                const result = geminiService.performSmartIdFilling(mockData);
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `测试成功！
修复前: languages_id_array = []
修复后: languages_id_array = ${JSON.stringify(result.languages_id_array, null, 2)}

预期结果: 应该包含英文语言ID (2)`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        function testEmptyArray() {
            const resultDiv = document.getElementById('result3');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const geminiService = window.OTA.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                // 测试不同的空值情况
                const testCases = [
                    { name: '空数组', value: [] },
                    { name: '空对象', value: {} },
                    { name: 'null值', value: null },
                    { name: 'undefined', value: undefined }
                ];
                
                let results = [];
                
                testCases.forEach(testCase => {
                    const mockData = {
                        customer_name: "测试用户",
                        languages_id_array: testCase.value
                    };
                    
                    const result = geminiService.performSmartIdFilling(mockData);
                    results.push(`${testCase.name}: ${JSON.stringify(result.languages_id_array)}`);
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `空值修复测试结果：
${results.join('\n')}

所有情况都应该被修复为有效的语言ID对象`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        async function testFullOrder() {
            const resultDiv = document.getElementById('result4');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const geminiService = window.OTA.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                // 使用真实的订单文本
                const orderText = `客户：索润德
服务：送机
从：伊顿公寓·David's 潮流打卡点
到：吉隆坡国际机场T1
时间：明天上午10点
乘客：2人
联系：18616536999`;
                
                const result = await geminiService.parseOrderText(orderText);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `完整订单解析测试成功！

解析结果:
${JSON.stringify(result.data, null, 2)}

关键检查点:
- sub_category_id: ${result.data.sub_category_id} (应该是3-送机)
- car_type_id: ${result.data.car_type_id} (应该是5-5座车)
- driving_region_id: ${result.data.driving_region_id} (应该是1-吉隆坡)
- languages_id_array: ${JSON.stringify(result.data.languages_id_array)} (应该包含中文ID)
- incharge_by_backend_user_id: ${result.data.incharge_by_backend_user_id} (应该是37-smw)`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `解析失败: ${result.error}`;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
