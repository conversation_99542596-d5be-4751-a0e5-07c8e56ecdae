<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #28a745; }
        .warning { border-left: 4px solid #ffc107; }
        .error { border-left: 4px solid #dc3545; }
        .field-test {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .field-test div {
            padding: 5px;
            background: white;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 字段映射修复测试</h1>
        <p>测试修复后的字段映射功能，特别是dropoff→destination、pickup_date→date、pickup_time→time的映射</p>
        
        <div class="test-section">
            <h3>测试1：关键字段映射</h3>
            <button class="btn" onclick="testFieldMapping()">测试字段映射</button>
            <div id="result1" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试2：日期时间格式化</h3>
            <button class="btn" onclick="testDateTimeFormatting()">测试日期时间格式化</button>
            <div id="result2" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试3：负责人字段隐藏</h3>
            <button class="btn" onclick="testHiddenField()">检查负责人字段</button>
            <div id="result3" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>测试4：完整订单数据填充</h3>
            <button class="btn" onclick="testFullOrderFill()">测试完整订单填充</button>
            <div id="result4" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>字段映射表</h3>
            <div id="mappingTable"></div>
        </div>
    </div>

    <!-- 加载OTA系统模块 -->
    <script>
        window.OTA = window.OTA || {};
    </script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/ui-manager.js"></script>

    <script>
        // 等待模块加载
        document.addEventListener('DOMContentLoaded', function() {
            showMappingTable();
        });

        function showMappingTable() {
            const mappingDiv = document.getElementById('mappingTable');
            
            // 显示字段映射表
            const mappings = [
                { ai: 'dropoff', html: 'destination', desc: '目的地' },
                { ai: 'pickup_date', html: 'date', desc: '日期' },
                { ai: 'pickup_time', html: 'time', desc: '时间' },
                { ai: 'customer_name', html: 'customerName', desc: '客户姓名' },
                { ai: 'customer_contact', html: 'customerContact', desc: '联系电话' },
                { ai: 'passenger_count', html: 'passengerNumber', desc: '乘客人数' },
                { ai: 'sub_category_id', html: 'subCategoryId', desc: '子分类' },
                { ai: 'car_type_id', html: 'carTypeId', desc: '车型' },
                { ai: 'incharge_by_backend_user_id', html: 'inchargeByBackendUserId', desc: '负责人(隐藏)' }
            ];
            
            let html = '<h4>关键字段映射表</h4>';
            mappings.forEach(mapping => {
                html += `<div class="field-test">
                    <div><strong>AI字段:</strong> ${mapping.ai}</div>
                    <div><strong>HTML元素:</strong> ${mapping.html}</div>
                    <div><strong>说明:</strong> ${mapping.desc}</div>
                </div>`;
            });
            
            mappingDiv.innerHTML = html;
        }

        function testFieldMapping() {
            const resultDiv = document.getElementById('result1');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const uiManager = window.OTA.uiManager;
                if (!uiManager) {
                    throw new Error('UI管理器未加载');
                }
                
                // 模拟AI解析结果
                const mockData = {
                    customer_name: "索润德",
                    customer_contact: "18616536999",
                    pickup: "伊顿公寓·David's 潮流打卡点",
                    dropoff: "吉隆坡国际机场T1",  // 关键测试字段
                    pickup_date: "2025-07-05",    // 关键测试字段
                    pickup_time: "12:10",         // 关键测试字段
                    passenger_count: 3,
                    sub_category_id: 3,
                    car_type_id: 5,
                    driving_region_id: 1,
                    incharge_by_backend_user_id: 37
                };
                
                // 执行字段填充
                uiManager.fillFormFromData(mockData);
                
                // 检查填充结果
                const results = [];
                const testFields = [
                    { ai: 'dropoff', element: 'destination', expected: '吉隆坡国际机场T1' },
                    { ai: 'pickup_date', element: 'date', expected: '2025-07-05' },
                    { ai: 'pickup_time', element: 'time', expected: '12:10' },
                    { ai: 'customer_name', element: 'customerName', expected: '索润德' },
                    { ai: 'pickup', element: 'pickup', expected: '伊顿公寓·David\'s 潮流打卡点' }
                ];
                
                testFields.forEach(test => {
                    const element = document.getElementById(test.element);
                    const actualValue = element ? element.value : 'ELEMENT_NOT_FOUND';
                    const success = actualValue === test.expected;
                    
                    results.push(`${test.ai} → ${test.element}: ${success ? '✅' : '❌'}
  期望: ${test.expected}
  实际: ${actualValue}`);
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `字段映射测试结果：

${results.join('\n\n')}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        function testDateTimeFormatting() {
            const resultDiv = document.getElementById('result2');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const uiManager = window.OTA.uiManager;
                if (!uiManager) {
                    throw new Error('UI管理器未加载');
                }
                
                // 测试各种日期时间格式
                const testCases = [
                    { date: '2025-07-05', time: '12:10' },
                    { date: '2025/07/05', time: '14:30:00' },
                    { date: 'July 5, 2025', time: '9:15 AM' },
                    { date: '05-07-2025', time: '23:45' }
                ];
                
                let results = [];
                
                testCases.forEach((testCase, index) => {
                    const formattedDate = uiManager.formatDateForInput(testCase.date);
                    const formattedTime = uiManager.formatTimeForInput(testCase.time);
                    
                    results.push(`测试 ${index + 1}:
  原始日期: ${testCase.date} → ${formattedDate}
  原始时间: ${testCase.time} → ${formattedTime}`);
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `日期时间格式化测试结果：

${results.join('\n\n')}

所有结果都应该是 YYYY-MM-DD 和 HH:MM 格式`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        function testHiddenField() {
            const resultDiv = document.getElementById('result3');
            
            try {
                const inchargeField = document.getElementById('inchargeByBackendUserId');
                const parentDiv = inchargeField ? inchargeField.closest('.form-group') : null;
                
                if (!inchargeField) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 负责人字段元素未找到';
                    return;
                }
                
                const isHidden = parentDiv && (
                    parentDiv.style.display === 'none' ||
                    getComputedStyle(parentDiv).display === 'none'
                );
                
                const hasValue = inchargeField.value !== '';
                
                resultDiv.className = isHidden ? 'result success' : 'result warning';
                resultDiv.innerHTML = `负责人字段检查结果：

字段存在: ${inchargeField ? '✅' : '❌'}
字段隐藏: ${isHidden ? '✅' : '❌'}
字段有值: ${hasValue ? '✅' : '❌'}
当前值: ${inchargeField.value || '(空)'}

${isHidden ? '✅ 负责人字段已正确隐藏' : '⚠️ 负责人字段仍然可见'}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }

        function testFullOrderFill() {
            const resultDiv = document.getElementById('result4');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const uiManager = window.OTA.uiManager;
                if (!uiManager) {
                    throw new Error('UI管理器未加载');
                }
                
                // 使用真实的AI解析数据
                const realOrderData = {
                    "customer_name": "索润德",
                    "customer_contact": "18616536999",
                    "customer_email": null,
                    "flight_info": null,
                    "pickup": "伊顿公寓·David's 潮流打卡点",
                    "dropoff": "吉隆坡国际机场T1",
                    "pickup_date": "2025-07-05",
                    "pickup_time": "12:10",
                    "passenger_count": 3,
                    "luggage_count": null,
                    "sub_category_id": 3,
                    "car_type_id": 5,
                    "driving_region_id": 1,
                    "languages_id_array": [],
                    "remark": null,
                    "flight_type": "Departure",
                    "airport": "吉隆坡国际机场T1",
                    "hotel_name": "伊顿公寓",
                    "ota_reference_number": "2809386517829349765",
                    "price": 140,
                    "incharge_by_backend_user_id": 37
                };
                
                // 执行完整填充
                uiManager.fillFormFromData(realOrderData);
                
                // 检查关键字段
                const keyFields = [
                    { name: '客户姓名', id: 'customerName', expected: '索润德' },
                    { name: '联系电话', id: 'customerContact', expected: '18616536999' },
                    { name: '上车地点', id: 'pickup', expected: '伊顿公寓·David\'s 潮流打卡点' },
                    { name: '目的地', id: 'destination', expected: '吉隆坡国际机场T1' },
                    { name: '日期', id: 'date', expected: '2025-07-05' },
                    { name: '时间', id: 'time', expected: '12:10' },
                    { name: '乘客人数', id: 'passengerNumber', expected: '3' },
                    { name: 'OTA参考号', id: 'otaReferenceNumber', expected: '2809386517829349765' }
                ];
                
                let results = [];
                let successCount = 0;
                
                keyFields.forEach(field => {
                    const element = document.getElementById(field.id);
                    const actualValue = element ? element.value : 'ELEMENT_NOT_FOUND';
                    const success = actualValue === field.expected;
                    
                    if (success) successCount++;
                    
                    results.push(`${field.name}: ${success ? '✅' : '❌'}
  期望: ${field.expected}
  实际: ${actualValue}`);
                });
                
                const successRate = `${successCount}/${keyFields.length}`;
                
                resultDiv.className = successCount === keyFields.length ? 'result success' : 'result warning';
                resultDiv.innerHTML = `完整订单填充测试结果 (${successRate}):

${results.join('\n\n')}

${successCount === keyFields.length ? '🎉 所有关键字段都正确填充！' : '⚠️ 部分字段填充失败，请检查映射逻辑'}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
