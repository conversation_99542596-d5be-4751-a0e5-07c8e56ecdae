<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统数据调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .data-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .log-entry {
            background: #f8f9fa;
            border-left: 4px solid #6c757d;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry.error { border-left-color: #dc3545; }
        .log-entry.warning { border-left-color: #ffc107; }
        .log-entry.success { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OTA系统数据调试工具</h1>
        <p>用于诊断和验证系统数据加载、表单填充和智能ID填充功能</p>
        
        <div class="section">
            <h3>系统状态检查</h3>
            <button class="btn" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="btn" onclick="reloadSystemData()">重新加载系统数据</button>
            <button class="btn" onclick="testFormPopulation()">测试表单填充</button>
            <div id="systemStatus"></div>
        </div>
        
        <div class="section">
            <h3>数据源对比</h3>
            <div id="dataComparison"></div>
        </div>
        
        <div class="section">
            <h3>智能ID填充测试</h3>
            <textarea id="testOrderText" rows="5" cols="80" placeholder="输入测试订单文本...">
客户：索润德
服务：送机
从：伊顿公寓
到：吉隆坡国际机场T1
时间：明天上午10点
乘客：2人
联系：+60123456789
            </textarea>
            <br>
            <button class="btn" onclick="testSmartIdFilling()">测试智能ID填充</button>
            <div id="smartIdResults"></div>
        </div>
        
        <div class="section">
            <h3>实时日志</h3>
            <button class="btn" onclick="clearLogs()">清空日志</button>
            <div id="logContainer" style="max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- 加载OTA系统模块 -->
    <script>
        // 创建OTA命名空间
        window.OTA = window.OTA || {};
    </script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/ui-manager.js"></script>

    <script>
        // 调试工具脚本
        let logContainer;
        
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('logContainer');
            
            // 监听日志更新
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.on((logEntry) => {
                    addLogEntry(logEntry);
                });
            }
            
            // 自动执行初始检查
            setTimeout(checkSystemStatus, 1000);
        });
        
        function addLogEntry(logEntry) {
            if (!logContainer) return;
            
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry ${logEntry.level}`;
            logDiv.innerHTML = `
                <strong>[${new Date(logEntry.timestamp).toLocaleTimeString()}]</strong>
                <span class="level">[${logEntry.level.toUpperCase()}]</span>
                ${logEntry.message}
                ${logEntry.data ? '<br><small>' + JSON.stringify(logEntry.data, null, 2) + '</small>' : ''}
            `;
            
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            if (logContainer) {
                logContainer.innerHTML = '';
            }
        }
        
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = '<div class="status">检查中...</div>';
            
            try {
                const appState = window.OTA.appState;
                const apiService = window.OTA.apiService;
                const geminiService = window.OTA.geminiService;
                
                // 检查模块加载状态
                const moduleStatus = {
                    appState: !!appState,
                    apiService: !!apiService,
                    geminiService: !!geminiService,
                    uiManager: !!window.OTA.uiManager
                };
                
                // 检查系统数据
                const systemData = appState ? appState.get('systemData') : null;
                const dataStatus = {
                    backendUsers: systemData?.backendUsers?.length || 0,
                    carTypes: systemData?.carTypes?.length || 0,
                    subCategories: systemData?.subCategories?.length || 0,
                    drivingRegions: systemData?.drivingRegions?.length || 0,
                    languages: systemData?.languages?.length || 0
                };
                
                // 检查静态数据
                const staticData = apiService ? apiService.staticData : null;
                const staticStatus = {
                    backendUsers: staticData?.backendUsers?.length || 0,
                    carTypes: staticData?.carTypes?.length || 0,
                    subCategories: staticData?.subCategories?.length || 0,
                    drivingRegions: staticData?.drivingRegions?.length || 0,
                    languages: staticData?.languages?.length || 0
                };
                
                let html = '<div class="data-grid">';
                
                // 模块状态
                html += '<div class="data-item">';
                html += '<h4>模块加载状态</h4>';
                Object.entries(moduleStatus).forEach(([key, value]) => {
                    const status = value ? 'success' : 'error';
                    html += `<div class="status ${status}">${key}: ${value ? '✓' : '✗'}</div>`;
                });
                html += '</div>';
                
                // 系统数据状态
                html += '<div class="data-item">';
                html += '<h4>AppState系统数据</h4>';
                Object.entries(dataStatus).forEach(([key, value]) => {
                    const status = value > 0 ? 'success' : 'warning';
                    html += `<div class="status ${status}">${key}: ${value}</div>`;
                });
                html += '</div>';
                
                // 静态数据状态
                html += '<div class="data-item">';
                html += '<h4>ApiService静态数据</h4>';
                Object.entries(staticStatus).forEach(([key, value]) => {
                    const status = value > 0 ? 'success' : 'error';
                    html += `<div class="status ${status}">${key}: ${value}</div>`;
                });
                html += '</div>';
                
                html += '</div>';
                statusDiv.innerHTML = html;
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">检查失败: ${error.message}</div>`;
            }
        }
        
        async function reloadSystemData() {
            try {
                const apiService = window.OTA.apiService;
                if (apiService) {
                    await apiService.getAllSystemData();
                    checkSystemStatus();
                }
            } catch (error) {
                console.error('重新加载系统数据失败:', error);
            }
        }
        
        function testFormPopulation() {
            // 这里可以测试表单填充逻辑
            console.log('测试表单填充功能...');
        }
        
        async function testSmartIdFilling() {
            const orderText = document.getElementById('testOrderText').value;
            const resultsDiv = document.getElementById('smartIdResults');
            
            if (!orderText.trim()) {
                resultsDiv.innerHTML = '<div class="status error">请输入测试订单文本</div>';
                return;
            }
            
            resultsDiv.innerHTML = '<div class="status">处理中...</div>';
            
            try {
                const geminiService = window.OTA.geminiService;
                if (!geminiService) {
                    throw new Error('Gemini服务未加载');
                }
                
                const result = await geminiService.parseOrderText(orderText);
                
                let html = '<div class="data-grid">';
                html += '<div class="data-item">';
                html += '<h4>解析结果</h4>';
                html += `<div class="status ${result.success ? 'success' : 'error'}">`;
                html += `状态: ${result.success ? '成功' : '失败'}`;
                html += '</div>';
                
                if (result.success && result.data) {
                    html += '<h5>提取的字段:</h5>';
                    Object.entries(result.data).forEach(([key, value]) => {
                        html += `<div><strong>${key}:</strong> ${JSON.stringify(value)}</div>`;
                    });
                }
                
                if (result.error) {
                    html += `<div class="status error">错误: ${result.error}</div>`;
                }
                
                html += '</div>';
                html += '</div>';
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
